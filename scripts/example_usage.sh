#!/bin/bash

# API测试脚本使用示例

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
API_SCRIPT="$SCRIPT_DIR/product_api_test.sh"

echo "=== 商品API测试脚本使用示例 ==="
echo ""

# 检查脚本是否存在
if [[ ! -f "$API_SCRIPT" ]]; then
    echo "错误: 找不到API测试脚本 $API_SCRIPT"
    exit 1
fi

echo "1. 显示帮助信息"
echo "命令: $API_SCRIPT product_api_test::common::show_help"
echo ""
$API_SCRIPT product_api_test::common::show_help
echo ""
echo "按回车键继续..."
read

echo "2. 用户登录测试"
echo "命令: $API_SCRIPT product_api_test::auth::login"
echo ""
$API_SCRIPT product_api_test::auth::login
echo ""
echo "按回车键继续..."
read

echo "3. 获取精品推荐商品列表"
echo "命令: $API_SCRIPT product_api_test::product::get_featured_list 1 5"
echo ""
$API_SCRIPT product_api_test::product::get_featured_list 1 5
echo ""
echo "按回车键继续..."
read

echo "4. 获取热门榜单商品列表"
echo "命令: $API_SCRIPT product_api_test::product::get_hot_list 1 3"
echo ""
$API_SCRIPT product_api_test::product::get_hot_list 1 3
echo ""
echo "按回车键继续..."
read

echo "5. 搜索商品"
echo "命令: $API_SCRIPT product_api_test::product::search '手机' 1 3"
echo ""
$API_SCRIPT product_api_test::product::search "手机" 1 3
echo ""
echo "按回车键继续..."
read

echo "6. 获取商品详情（需要提供有效的商品ID）"
echo "请输入商品ID（默认为1）:"
read product_id
product_id=${product_id:-1}
echo "命令: $API_SCRIPT product_api_test::product::get_detail $product_id"
echo ""
$API_SCRIPT product_api_test::product::get_detail "$product_id"
echo ""
echo "按回车键继续..."
read

echo "7. 获取用户信息"
echo "命令: $API_SCRIPT product_api_test::user::get_info"
echo ""
$API_SCRIPT product_api_test::user::get_info
echo ""
echo "按回车键继续..."
read

echo "8. 运行完整测试流程"
echo "命令: $API_SCRIPT product_api_test::test::run_full_test"
echo ""
$API_SCRIPT product_api_test::test::run_full_test
echo ""

echo "=== 示例演示完成 ==="
echo ""
echo "更多高级功能："
echo "- 环境切换: $API_SCRIPT product_api_test::config::set_env dev"
echo "- 批量测试: $API_SCRIPT product_api_test::test::batch_product_details 1 2 3"
echo "- 性能测试: $API_SCRIPT product_api_test::test::performance_test"
echo ""
echo "详细使用说明请运行: $API_SCRIPT product_api_test::common::show_help"
