# 商品API测试脚本

这是一套完整的商品API测试脚本，支持登录、获取商品列表、获取商品详情等功能。

## 文件说明

- `product_api_test.sh` - 主要的API测试脚本
- `config.env` - 配置文件
- `example_usage.sh` - 使用示例脚本
- `README.md` - 说明文档

## 快速开始

### 1. 基本使用

```bash
# 显示帮助信息
./scripts/product_api_test.sh product_api_test::common::show_help

# 用户登录
./scripts/product_api_test.sh product_api_test::auth::login

# 获取精品推荐商品列表
./scripts/product_api_test.sh product_api_test::product::get_featured_list 1 10

# 获取商品详情
./scripts/product_api_test.sh product_api_test::product::get_detail 123

# 运行完整测试
./scripts/product_api_test.sh product_api_test::test::run_full_test
```

### 2. 运行示例演示

```bash
./scripts/example_usage.sh
```

## 函数命名规范

所有函数都遵循 `<文件名>::<模块>::<功能>` 的命名规范：

- `product_api_test::auth::login` - 用户登录
- `product_api_test::product::get_featured_list` - 获取精品推荐
- `product_api_test::product::get_detail` - 获取商品详情
- `product_api_test::common::log_info` - 日志输出

## 可用功能

### 认证模块 (auth)
- `product_api_test::auth::login [账号] [密码]` - 用户登录

### 商品模块 (product)
- `product_api_test::product::get_featured_list [页码] [每页数量]` - 精品推荐
- `product_api_test::product::get_hot_list [页码] [每页数量]` - 热门榜单
- `product_api_test::product::get_new_list [页码] [每页数量]` - 首发新品
- `product_api_test::product::get_promotion_list [页码] [每页数量]` - 促销单品
- `product_api_test::product::get_detail <商品ID> [类型]` - 商品详情
- `product_api_test::product::search <关键词> [页码] [每页数量]` - 搜索商品

### 用户模块 (user)
- `product_api_test::user::get_info` - 获取用户信息

### 测试模块 (test)
- `product_api_test::test::run_full_test` - 完整测试流程
- `product_api_test::test::batch_product_details <商品ID1> <商品ID2> ...` - 批量测试商品详情
- `product_api_test::test::performance_test [端点] [请求数] [并发数]` - 性能测试

### 配置模块 (config)
- `product_api_test::config::set_env <环境名>` - 切换环境 (dev/test/prod)

### 工具模块 (common)
- `product_api_test::common::show_help` - 显示帮助
- `product_api_test::common::cleanup` - 清理临时文件
- `product_api_test::common::save_response <响应数据> [文件名]` - 保存响应
- `product_api_test::common::format_json <JSON数据>` - 格式化JSON

## 配置说明

编辑 `config.env` 文件来修改配置：

```bash
# 服务器配置
BASE_URL="http://localhost:8081"

# 默认登录账号
DEFAULT_ACCOUNT="***********"
DEFAULT_PASSWORD="crmeb@123456"

# 环境配置
DEV_BASE_URL="http://localhost:8081"
TEST_BASE_URL="http://test.example.com"
PROD_BASE_URL="https://api.example.com"
```

## 环境切换

支持多环境配置：

```bash
# 切换到开发环境
./scripts/product_api_test.sh product_api_test::config::set_env dev

# 切换到测试环境
./scripts/product_api_test.sh product_api_test::config::set_env test

# 切换到生产环境
./scripts/product_api_test.sh product_api_test::config::set_env prod
```

## 高级功能

### 批量测试
```bash
# 批量测试多个商品详情
./scripts/product_api_test.sh product_api_test::test::batch_product_details 1 2 3 4 5
```

### 性能测试
```bash
# 性能测试：100个请求，5个并发
./scripts/product_api_test.sh product_api_test::test::performance_test /api/front/index/product/1 100 5
```

### 保存响应
```bash
# 获取商品列表并保存响应
response=$(./scripts/product_api_test.sh product_api_test::product::get_featured_list)
./scripts/product_api_test.sh product_api_test::common::save_response "$response" "product_list.json"
```

## 依赖要求

- `curl` - HTTP请求工具
- `jq` - JSON处理工具（可选，用于格式化输出）
- `bc` - 计算器（用于性能测试）

## 注意事项

1. 首次使用前请确保服务器正在运行
2. 修改 `config.env` 中的服务器地址和登录凭据
3. 某些功能需要有效的商品ID
4. 性能测试时注意不要对生产环境造成压力

## 故障排除

### 常见问题

1. **连接失败**: 检查 `BASE_URL` 配置和服务器状态
2. **认证失败**: 检查账号密码是否正确
3. **权限错误**: 确保脚本有执行权限 (`chmod +x`)
4. **JSON格式错误**: 安装 `jq` 工具获得更好的输出格式

### 调试模式

设置环境变量启用详细日志：
```bash
export LOG_LEVEL=DEBUG
./scripts/product_api_test.sh product_api_test::test::run_full_test
```
