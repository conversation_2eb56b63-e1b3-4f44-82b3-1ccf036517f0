#!/bin/bash

# 商品API测试脚本
# 包含登录、获取商品列表、获取商品详情等功能

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/config.env"

# 加载配置文件
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
fi

# 全局变量
BASE_URL="${BASE_URL:-http://localhost:8081}"
TOKEN=""
COOKIE_FILE="${COOKIE_FILE:-/tmp/api_cookies.txt}"
DEFAULT_ACCOUNT="${DEFAULT_ACCOUNT:-***********}"
DEFAULT_PASSWORD="${DEFAULT_PASSWORD:-crmeb@123456}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
product_api_test::common::log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

product_api_test::common::log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

product_api_test::common::log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

product_api_test::common::log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查响应状态
product_api_test::common::check_response() {
    local response="$1"
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | sed '$d')
    
    if [[ "$http_code" -eq 200 ]]; then
        product_api_test::common::log_info "请求成功 (HTTP $http_code)"
        echo "$body"
        return 0
    else
        product_api_test::common::log_error "请求失败 (HTTP $http_code)"
        echo "$body"
        return 1
    fi
}

# 用户登录
product_api_test::auth::login() {
    local account="${1:-$DEFAULT_ACCOUNT}"
    local password="${2:-$DEFAULT_PASSWORD}"
    
    product_api_test::common::log_info "开始用户登录..."
    product_api_test::common::log_debug "账号: $account"
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -c "$COOKIE_FILE" \
        -d "{\"account\":\"$account\",\"password\":\"$password\"}" \
        "$BASE_URL/api/front/login")
    
    if product_api_test::common::check_response "$response"; then
        local body=$(echo "$response" | sed '$d')
        TOKEN=$(echo "$body" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        
        if [[ -n "$TOKEN" ]]; then
            product_api_test::common::log_info "登录成功，Token: ${TOKEN:0:20}..."
            return 0
        else
            product_api_test::common::log_error "登录失败，未获取到Token"
            return 1
        fi
    else
        return 1
    fi
}

# 获取商品列表 - 精品推荐
product_api_test::product::get_featured_list() {
    local page="${1:-1}"
    local limit="${2:-10}"
    
    product_api_test::common::log_info "获取精品推荐商品列表..."
    product_api_test::common::log_debug "页码: $page, 每页数量: $limit"
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X GET \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -b "$COOKIE_FILE" \
        "$BASE_URL/api/front/index/product/1?page=$page&limit=$limit")
    
    product_api_test::common::check_response "$response"
}

# 获取商品列表 - 热门榜单
product_api_test::product::get_hot_list() {
    local page="${1:-1}"
    local limit="${2:-10}"
    
    product_api_test::common::log_info "获取热门榜单商品列表..."
    product_api_test::common::log_debug "页码: $page, 每页数量: $limit"
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X GET \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -b "$COOKIE_FILE" \
        "$BASE_URL/api/front/index/product/2?page=$page&limit=$limit")
    
    product_api_test::common::check_response "$response"
}

# 获取商品列表 - 首发新品
product_api_test::product::get_new_list() {
    local page="${1:-1}"
    local limit="${2:-10}"
    
    product_api_test::common::log_info "获取首发新品商品列表..."
    product_api_test::common::log_debug "页码: $page, 每页数量: $limit"
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X GET \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -b "$COOKIE_FILE" \
        "$BASE_URL/api/front/index/product/3?page=$page&limit=$limit")
    
    product_api_test::common::check_response "$response"
}

# 获取商品列表 - 促销单品
product_api_test::product::get_promotion_list() {
    local page="${1:-1}"
    local limit="${2:-10}"
    
    product_api_test::common::log_info "获取促销单品商品列表..."
    product_api_test::common::log_debug "页码: $page, 每页数量: $limit"
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X GET \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -b "$COOKIE_FILE" \
        "$BASE_URL/api/front/index/product/4?page=$page&limit=$limit")
    
    product_api_test::common::check_response "$response"
}

# 获取商品详情
product_api_test::product::get_detail() {
    local product_id="$1"
    local type="${2:-normal}"
    
    if [[ -z "$product_id" ]]; then
        product_api_test::common::log_error "商品ID不能为空"
        return 1
    fi
    
    product_api_test::common::log_info "获取商品详情..."
    product_api_test::common::log_debug "商品ID: $product_id, 类型: $type"
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X GET \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -b "$COOKIE_FILE" \
        "$BASE_URL/api/front/product/detail/$product_id?type=$type")
    
    product_api_test::common::check_response "$response"
}

# 搜索商品
product_api_test::product::search() {
    local keyword="$1"
    local page="${2:-1}"
    local limit="${3:-10}"
    
    if [[ -z "$keyword" ]]; then
        product_api_test::common::log_error "搜索关键词不能为空"
        return 1
    fi
    
    product_api_test::common::log_info "搜索商品..."
    product_api_test::common::log_debug "关键词: $keyword, 页码: $page, 每页数量: $limit"
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X GET \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -b "$COOKIE_FILE" \
        "$BASE_URL/api/front/product/search?keyword=$(echo "$keyword" | sed 's/ /%20/g')&page=$page&limit=$limit")
    
    product_api_test::common::check_response "$response"
}

# 获取用户信息
product_api_test::user::get_info() {
    product_api_test::common::log_info "获取用户信息..."
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X GET \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -b "$COOKIE_FILE" \
        "$BASE_URL/api/front/user")
    
    product_api_test::common::check_response "$response"
}

# 设置环境
product_api_test::config::set_env() {
    local env="$1"

    case "$env" in
        "dev"|"development")
            BASE_URL="$DEV_BASE_URL"
            DEFAULT_ACCOUNT="$DEV_ACCOUNT"
            DEFAULT_PASSWORD="$DEV_PASSWORD"
            product_api_test::common::log_info "切换到开发环境: $BASE_URL"
            ;;
        "test"|"testing")
            BASE_URL="$TEST_BASE_URL"
            DEFAULT_ACCOUNT="$TEST_ACCOUNT"
            DEFAULT_PASSWORD="$TEST_PASSWORD"
            product_api_test::common::log_info "切换到测试环境: $BASE_URL"
            ;;
        "prod"|"production")
            BASE_URL="$PROD_BASE_URL"
            DEFAULT_ACCOUNT="$PROD_ACCOUNT"
            DEFAULT_PASSWORD="$PROD_PASSWORD"
            product_api_test::common::log_info "切换到生产环境: $BASE_URL"
            ;;
        *)
            product_api_test::common::log_error "未知环境: $env"
            product_api_test::common::log_info "可用环境: dev, test, prod"
            return 1
            ;;
    esac
}

# 保存响应到文件
product_api_test::common::save_response() {
    local response="$1"
    local filename="$2"

    if [[ -z "$filename" ]]; then
        filename="/tmp/api_response_$(date +%Y%m%d_%H%M%S).json"
    fi

    echo "$response" | sed '$d' > "$filename"
    product_api_test::common::log_info "响应已保存到: $filename"
}

# 格式化JSON输出
product_api_test::common::format_json() {
    local json_data="$1"

    if command -v jq >/dev/null 2>&1; then
        echo "$json_data" | jq '.'
    else
        echo "$json_data"
    fi
}

# 清理临时文件
product_api_test::common::cleanup() {
    product_api_test::common::log_info "清理临时文件..."
    [[ -f "$COOKIE_FILE" ]] && rm -f "$COOKIE_FILE"
}

# 完整测试流程
product_api_test::test::run_full_test() {
    product_api_test::common::log_info "开始完整API测试流程..."
    
    # 1. 登录
    if ! product_api_test::auth::login; then
        product_api_test::common::log_error "登录失败，终止测试"
        return 1
    fi
    
    echo ""
    
    # 2. 获取用户信息
    product_api_test::user::get_info
    echo ""
    
    # 3. 获取各类商品列表
    product_api_test::product::get_featured_list 1 5
    echo ""
    
    product_api_test::product::get_hot_list 1 5
    echo ""
    
    product_api_test::product::get_new_list 1 5
    echo ""
    
    # 4. 搜索商品
    product_api_test::product::search "手机" 1 3
    echo ""
    
    product_api_test::common::log_info "完整测试流程结束"
}

# 批量测试商品详情
product_api_test::test::batch_product_details() {
    local product_ids=("$@")

    if [[ ${#product_ids[@]} -eq 0 ]]; then
        product_api_test::common::log_error "请提供商品ID列表"
        return 1
    fi

    product_api_test::common::log_info "开始批量测试商品详情..."

    # 确保已登录
    if [[ -z "$TOKEN" ]]; then
        if ! product_api_test::auth::login; then
            product_api_test::common::log_error "登录失败，终止测试"
            return 1
        fi
    fi

    local success_count=0
    local fail_count=0

    for product_id in "${product_ids[@]}"; do
        product_api_test::common::log_info "测试商品ID: $product_id"

        if product_api_test::product::get_detail "$product_id"; then
            ((success_count++))
            product_api_test::common::log_info "✓ 商品 $product_id 测试成功"
        else
            ((fail_count++))
            product_api_test::common::log_error "✗ 商品 $product_id 测试失败"
        fi

        echo ""
        sleep 1  # 避免请求过于频繁
    done

    product_api_test::common::log_info "批量测试完成: 成功 $success_count 个, 失败 $fail_count 个"
}

# 性能测试
product_api_test::test::performance_test() {
    local endpoint="${1:-/api/front/index/product/1}"
    local requests="${2:-10}"
    local concurrent="${3:-1}"

    product_api_test::common::log_info "开始性能测试..."
    product_api_test::common::log_debug "端点: $endpoint, 请求数: $requests, 并发数: $concurrent"

    # 确保已登录
    if [[ -z "$TOKEN" ]]; then
        if ! product_api_test::auth::login; then
            product_api_test::common::log_error "登录失败，终止测试"
            return 1
        fi
    fi

    local start_time=$(date +%s.%N)
    local success_count=0
    local fail_count=0

    for ((i=1; i<=requests; i++)); do
        local response=$(curl -s -w "\n%{http_code}" \
            -X GET \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $TOKEN" \
            -b "$COOKIE_FILE" \
            "$BASE_URL$endpoint")

        local http_code=$(echo "$response" | tail -n1)

        if [[ "$http_code" -eq 200 ]]; then
            ((success_count++))
            echo -n "."
        else
            ((fail_count++))
            echo -n "x"
        fi

        if [[ $((i % 50)) -eq 0 ]]; then
            echo " [$i/$requests]"
        fi
    done

    echo ""
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    local avg_time=$(echo "scale=3; $duration / $requests" | bc)

    product_api_test::common::log_info "性能测试完成:"
    product_api_test::common::log_info "  总请求数: $requests"
    product_api_test::common::log_info "  成功请求: $success_count"
    product_api_test::common::log_info "  失败请求: $fail_count"
    product_api_test::common::log_info "  总耗时: ${duration}s"
    product_api_test::common::log_info "  平均响应时间: ${avg_time}s"
}

# 显示帮助信息
product_api_test::common::show_help() {
    echo "商品API测试脚本使用说明："
    echo ""
    echo "基本用法："
    echo "  $0 <function_name> [参数...]"
    echo ""
    echo "可用函数："
    echo "  登录相关："
    echo "    product_api_test::auth::login [账号] [密码]"
    echo ""
    echo "  商品列表："
    echo "    product_api_test::product::get_featured_list [页码] [每页数量]"
    echo "    product_api_test::product::get_hot_list [页码] [每页数量]"
    echo "    product_api_test::product::get_new_list [页码] [每页数量]"
    echo "    product_api_test::product::get_promotion_list [页码] [每页数量]"
    echo ""
    echo "  商品详情："
    echo "    product_api_test::product::get_detail <商品ID> [类型]"
    echo ""
    echo "  商品搜索："
    echo "    product_api_test::product::search <关键词> [页码] [每页数量]"
    echo ""
    echo "  用户信息："
    echo "    product_api_test::user::get_info"
    echo ""
    echo "  测试流程："
    echo "    product_api_test::test::run_full_test"
    echo "    product_api_test::test::batch_product_details <商品ID1> <商品ID2> ..."
    echo "    product_api_test::test::performance_test [端点] [请求数] [并发数]"
    echo ""
    echo "  配置管理："
    echo "    product_api_test::config::set_env <环境名>"
    echo ""
    echo "  工具函数："
    echo "    product_api_test::common::cleanup"
    echo "    product_api_test::common::show_help"
    echo "    product_api_test::common::save_response <响应数据> [文件名]"
    echo "    product_api_test::common::format_json <JSON数据>"
    echo ""
    echo "示例："
    echo "  # 基本使用"
    echo "  $0 product_api_test::auth::login"
    echo "  $0 product_api_test::product::get_featured_list 1 10"
    echo "  $0 product_api_test::product::get_detail 123"
    echo ""
    echo "  # 环境切换"
    echo "  $0 product_api_test::config::set_env dev"
    echo "  $0 product_api_test::config::set_env test"
    echo ""
    echo "  # 批量测试"
    echo "  $0 product_api_test::test::batch_product_details 1 2 3 4 5"
    echo "  $0 product_api_test::test::performance_test /api/front/index/product/1 100 5"
    echo ""
    echo "  # 完整测试"
    echo "  $0 product_api_test::test::run_full_test"
}

# 主函数
main() {
    # 设置错误时退出
    set -e
    
    # 注册清理函数
    trap product_api_test::common::cleanup EXIT
    
    if [[ $# -eq 0 ]]; then
        product_api_test::common::show_help
        exit 0
    fi
    
    local function_name="$1"
    shift
    
    # 检查函数是否存在
    if declare -f "$function_name" > /dev/null; then
        "$function_name" "$@"
    else
        product_api_test::common::log_error "函数 '$function_name' 不存在"
        echo ""
        product_api_test::common::show_help
        exit 1
    fi
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
