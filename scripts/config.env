# API测试配置文件

# 服务器配置
BASE_URL="http://localhost:8081"
API_TIMEOUT=30

# 默认登录账号
DEFAULT_ACCOUNT="***********"
DEFAULT_PASSWORD="crmeb@123456"

# 分页配置
DEFAULT_PAGE=1
DEFAULT_LIMIT=10

# 测试商品ID（用于详情测试）
TEST_PRODUCT_ID="1"

# 日志配置
LOG_LEVEL="INFO"  # DEBUG, INFO, WARN, ERROR
LOG_FILE="/tmp/api_test.log"

# 临时文件路径
COOKIE_FILE="/tmp/api_cookies.txt"
TEMP_DIR="/tmp"

# 测试环境配置
# 开发环境
DEV_BASE_URL="http://localhost:8081"
DEV_ACCOUNT="***********"
DEV_PASSWORD="crmeb@123456"

# 测试环境
TEST_BASE_URL="http://test.example.com"
TEST_ACCOUNT="<EMAIL>"
TEST_PASSWORD="test123456"

# 生产环境
PROD_BASE_URL="https://api.example.com"
PROD_ACCOUNT="<EMAIL>"
PROD_PASSWORD="prod123456"
