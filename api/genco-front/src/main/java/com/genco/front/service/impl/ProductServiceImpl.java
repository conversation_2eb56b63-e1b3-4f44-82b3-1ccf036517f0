package com.genco.front.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.genco.common.constants.CategoryConstants;
import com.genco.common.constants.Constants;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.enums.ProductChannelEnum;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.brand.StoreBrand;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.model.product.StoreProductAttr;
import com.genco.common.model.product.StoreProductAttrValue;
import com.genco.common.model.product.StoreProductShareRecord;
import com.genco.common.model.record.UserVisitRecord;
import com.genco.common.model.system.SystemUserLevel;
import com.genco.common.model.user.User;
import com.genco.common.page.CommonPage;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.ProductListRequest;
import com.genco.common.request.ProductRequest;
import com.genco.common.request.ProductUrlSearchRequest;
import com.genco.common.response.*;
import com.genco.common.utils.CrmebUtil;
import com.genco.common.utils.RedisUtil;
import com.genco.common.vo.CategoryTreeVo;
import com.genco.common.vo.MyRecord;
import com.genco.front.service.ProductService;
import com.genco.service.delete.ProductUtils;
import com.genco.service.service.*;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202501Api;
import tiktokshop.open.sdk_java.invoke.ApiClient;
import tiktokshop.open.sdk_java.invoke.ApiResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * IndexServiceImpl 接口实现
 */
@Slf4j
@Service
public class ProductServiceImpl implements ProductService {

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreBrandService storeBrandService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private StoreProductReplyService storeProductReplyService;

    @Autowired
    private UserService userService;

    @Autowired
    private StoreProductRelationService storeProductRelationService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private ProductUtils productUtils;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StoreProductAttrService attrService;

    @Autowired
    private StoreProductAttrValueService storeProductAttrValueService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private StoreCartService cartService;

    @Autowired
    private UserVisitRecordService userVisitRecordService;

    @Autowired
    private TikTokProductIdService tikTokProductIdService;

    @Autowired
    private ProductImportService productImportService;

    @Autowired
    private StoreProductShareRecordService storeProductShareRecordService;

    /**
     * 获取类目
     *
     * @return List<CategoryTreeVo>
     */
    @Override
    public List<CategoryTreeVo> getCategory() {
        List<CategoryTreeVo> listTree = categoryService.getListTree(CategoryConstants.CATEGORY_TYPE_PRODUCT, 1, "");
        for (int i = 0; i < listTree.size(); ) {
            CategoryTreeVo categoryTreeVo = listTree.get(i);
            if (!categoryTreeVo.getPid().equals(0)) {
                listTree.remove(i);
                continue;
            }
            i++;
        }
        return listTree;
    }

    /**
     * 商品列表
     *
     * @return CommonPage<IndexProductResponse>
     */
    @Override
    public CommonPage<IndexProductResponse> getList(ProductRequest request, PageParamRequest pageRequest) {
        List<StoreProduct> storeProductList = storeProductService.findH5List(request, pageRequest);
        if (CollUtil.isEmpty(storeProductList)) {
            return CommonPage.restPage(new ArrayList<>());
        }
        CommonPage<StoreProduct> storeProductCommonPage = CommonPage.restPage(storeProductList);

        List<IndexProductResponse> productResponseArrayList = new ArrayList<>();
        for (StoreProduct storeProduct : storeProductList) {
            IndexProductResponse productResponse = new IndexProductResponse();
            List<Integer> activityList = CrmebUtil.stringToArrayInt(storeProduct.getActivity());
            // 活动类型默认：直接跳过
            if (activityList.get(0).equals(Constants.PRODUCT_TYPE_NORMAL)) {
                BeanUtils.copyProperties(storeProduct, productResponse);
                productResponseArrayList.add(productResponse);
                continue;
            }
            // 根据参与活动添加对应商品活动标示
            HashMap<Integer, ProductActivityItemResponse> activityByProduct =
                    productUtils.getActivityByProduct(storeProduct.getId(), storeProduct.getActivity());
            if (CollUtil.isNotEmpty(activityByProduct)) {
                for (Integer activity : activityList) {
                    if (activity.equals(Constants.PRODUCT_TYPE_NORMAL)) {
                        break;
                    }
                }
            }
            BeanUtils.copyProperties(storeProduct, productResponse);
            productResponseArrayList.add(productResponse);
        }
        CommonPage<IndexProductResponse> productResponseCommonPage = CommonPage.restPage(productResponseArrayList);
        BeanUtils.copyProperties(storeProductCommonPage, productResponseCommonPage, "list");
        return productResponseCommonPage;
    }

    /**
     * 根据商品ID 获取推荐商品列表
     * <p>
     * 目前只返回同品牌下的相似商品，后续在这里建设复杂的推荐功能
     * </p>
     *
     * @param id               请求参数
     * @param pageParamRequest 分页参数
     * @return CommonPage
     */
    @Override
    public CommonPage<IndexProductResponse> getRecommendList(Integer id, PageParamRequest pageParamRequest) {
        if (id == null) {
            return CommonPage.restPage(new ArrayList<>());
        }
        StoreProduct oStoreProduct = storeProductService.getById(id);
        if (oStoreProduct == null) {
            return CommonPage.restPage(new ArrayList<>());
        }
        List<StoreProduct> storeProductList = storeProductService.getBrandProduct(oStoreProduct.getBrand(),
                0, true, pageParamRequest);
        if (CollUtil.isEmpty(storeProductList)) {
            return CommonPage.restPage(new ArrayList<>());
        }
        CommonPage<StoreProduct> storeProductCommonPage = CommonPage.restPage(storeProductList);
        List<IndexProductResponse> productResponseArrayList = new ArrayList<>();
        for (StoreProduct storeProduct : storeProductList) {
            if (id.equals(storeProduct.getId())) {
                continue;
            }
            IndexProductResponse productResponse = getIndexProductResponse(storeProduct);
            productResponseArrayList.add(productResponse);
        }
        CommonPage<IndexProductResponse> productResponseCommonPage = CommonPage.restPage(productResponseArrayList);
        BeanUtils.copyProperties(storeProductCommonPage, productResponseCommonPage, "list");
        return productResponseCommonPage;
    }

    @NotNull
    private IndexProductResponse getIndexProductResponse(StoreProduct storeProduct) {
        IndexProductResponse productResponse = new IndexProductResponse();
        productResponse.setId(storeProduct.getId());

        // 获取当前用户信息
        User currentUser = userService.getInfo();

        // 获取用户等级佣金比例
        BigDecimal userCommissionRate = getUserCommissionRate(currentUser);

        // 先计算返现金额：商品价格 * 商品返现率 * 会员等级佣金比例
        if (storeProduct.getPrice() != null && storeProduct.getCashBackRate() != null &&
            storeProduct.getPrice().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal cashBackAmount = storeProduct.getPrice()
                    .multiply(storeProduct.getCashBackRate())
                    .multiply(userCommissionRate);
            productResponse.setCashBackAmount(cashBackAmount);

            // 再通过返现金额计算返现率：返现金额 / 商品价格
            BigDecimal cashBackRate = cashBackAmount.divide(storeProduct.getPrice(), 4, BigDecimal.ROUND_HALF_UP);
            productResponse.setCashBackRate(cashBackRate);
        }

        //补充临时的参数
        productResponse.setChannel(ProductChannelEnum.TIKTOK.getCode());
        productResponse.setMainImageUrl(storeProduct.getImage());
        productResponse.setSalesPrice(storeProduct.getPrice());
        productResponse.setMaxSalesPrice(storeProduct.getMaxSalesPrice());
        productResponse.setMinSalesPrice(storeProduct.getMinSalesPrice());
        productResponse.setTitle(storeProduct.getStoreName());
        productResponse.setShopName(storeProduct.getShopName());
        return productResponse;
    }

    /**
     * 根据用户等级获取对应的佣金比例
     *
     * @param user 用户信息
     * @return 佣金比例（百分比形式，需要除以100）
     */
    private BigDecimal getUserCommissionRate(User user) {
        if (user == null || user.getLevel() == null || user.getLevel() == 0) {
            // 普通用户默认佣金比例为0
            return BigDecimal.ZERO;
        }

        // 从用户等级表中获取佣金比例
        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (userLevel != null && userLevel.getCommissionRate() != null) {
            // 佣金比例存储为百分比，需要转换为小数
            return userLevel.getCommissionRate().divide(new BigDecimal("100"), 4, BigDecimal.ROUND_HALF_UP);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 获取商品详情
     *
     * @param id   商品编号
     * @param type normal-正常，video-视频
     * @return 商品详情信息
     */
    @Override
    public ProductDetailResponse getDetail(Integer id, String type) {
        // 获取用户
        User user = userService.getInfo();
        SystemUserLevel userLevel = null;
        if (ObjectUtil.isNotNull(user) && user.getLevel() > 0) {
            userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        }

        ProductDetailResponse productDetailResponse = new ProductDetailResponse();
        // 查询商品
        StoreProduct storeProduct = storeProductService.getH5Detail(id);

        // 获取用户等级佣金比例
        BigDecimal userCommissionRate = getUserCommissionRate(user);

        // 先计算返现金额：商品价格 * 商品返现率 * 会员等级佣金比例
        if (storeProduct.getPrice() != null && storeProduct.getCashBackRate() != null &&
            storeProduct.getPrice().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal cashBackAmount = storeProduct.getPrice()
                    .multiply(storeProduct.getCashBackRate())
                    .multiply(userCommissionRate);
            storeProduct.setCashBackAmount(cashBackAmount);

            // 再通过返现金额计算返现率：返现金额 / 商品价格
            BigDecimal cashBackRate = cashBackAmount.divide(storeProduct.getPrice(), 4, BigDecimal.ROUND_HALF_UP);
            storeProduct.setCashBackRate(cashBackRate);
        }
        if (ObjectUtil.isNotNull(userLevel)) {
            storeProduct.setVipPrice(storeProduct.getPrice());
        }
        productDetailResponse.setProductInfo(storeProduct);

        // 获取商品规格
        List<StoreProductAttr> attrList = attrService.getListByProductIdAndType(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
        // 根据制式设置attr属性
        productDetailResponse.setProductAttr(attrList);

        // 获取品牌信息
        StoreBrand storeBrand = storeBrandService.getByBrandCode(storeProduct.getBrand());
        productDetailResponse.setStoreBrand(storeBrand);

        // 根据制式设置sku属性
        HashMap<String, Object> skuMap = CollUtil.newHashMap();
        List<StoreProductAttrValue> storeProductAttrValues = storeProductAttrValueService.getListByProductIdAndType(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
        for (StoreProductAttrValue storeProductAttrValue : storeProductAttrValues) {
            StoreProductAttrValueResponse atr = new StoreProductAttrValueResponse();
            BeanUtils.copyProperties(storeProductAttrValue, atr);
            // 设置会员价
            if (ObjectUtil.isNotNull(userLevel)) {
                atr.setVipPrice(atr.getPrice());
            }
            skuMap.put(atr.getSuk(), atr);
        }
        productDetailResponse.setProductValue(skuMap);

        // 用户收藏、分销返佣
        if (ObjectUtil.isNotNull(user)) {
            // 查询用户是否收藏收藏
            user = userService.getInfo();
            productDetailResponse.setUserCollect(!storeProductRelationService.getLikeOrCollectByUser(user.getUid(), id, false).isEmpty());
            // 判断是否开启分销
            String brokerageFuncStatus = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_BROKERAGE_FUNC_STATUS);
        } else {
            productDetailResponse.setUserCollect(false);
        }
        // 商品活动
        List<ProductActivityItemResponse> activityAllH5 = productUtils.getProductAllActivity(storeProduct);
        productDetailResponse.setActivityAllH5(activityAllH5);

        // 商品浏览量+1
        StoreProduct updateProduct = new StoreProduct();
        updateProduct.setId(id);
        updateProduct.setBrowse(storeProduct.getBrowse() + 1);
        storeProductService.updateById(updateProduct);

        // 保存用户访问记录
        if (userService.getUserId() > 0) {
            UserVisitRecord visitRecord = new UserVisitRecord();
            visitRecord.setDate(DateUtil.date().toString("yyyy-MM-dd"));
            visitRecord.setUid(userService.getUserId());
            visitRecord.setVisitType(2);
            userVisitRecordService.save(visitRecord);
        }

        return productDetailResponse;
    }

    /**
     * 生成商品链接
     *
     * @param id      商品ID
     * @param channel 商品渠道
     * @return 分享链接地址
     */
    @Override
    public ProductShareLinkResponse getProductShareLink(Integer id, String channel) {

        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY));
        apiClient.setSecret(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET));
        apiClient.setTokens(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ACCESS_TOKEN));

        AffiliateCreatorV202501Api affiliateCreatorV202501Api = new AffiliateCreatorV202501Api(apiClient);
        ProductShareLinkResponse productShareLinkResponse = new ProductShareLinkResponse();
        try {
            //获取商品链接
            List<Integer> ids = new ArrayList<>();
            ids.add(id);
            List<StoreProduct> storeProductList = storeProductService.getListInIds(ids);
            if (storeProductList == null || storeProductList.isEmpty()) {
                //没有查找到商品
                return productShareLinkResponse;
            }
            StoreProduct productInfo = storeProductList.get(0);
            User user = userService.getInfo();
            //组装链接生成请求
            GenerateAffiliateSharingLinkRequestBody requestBody = new GenerateAffiliateSharingLinkRequestBody();
            requestBody.setChannel(productInfo.getChannel());
            GenerateAffiliateSharingLinkRequestBodyMaterial material = new GenerateAffiliateSharingLinkRequestBodyMaterial();
            material.setCampaignUrl(productInfo.getImage());
            material.setId(productInfo.getOutProductId());
            material.setType("1");
            requestBody.setMaterial(material);
            List<String> tags = new ArrayList<>();
            //标签设置成用户uid，用户订单进行关联 TODO 这里后面有时间最好设置成一个用户唯一的hash值来进行关联
            tags.add("" + user.getUid());
            requestBody.setTags(tags);
            ApiResponse<GenerateAffiliateSharingLinkResponse> responseInfo = affiliateCreatorV202501Api.
                    affiliateCreator202501AffiliateSharingLinksGenerateBatchPostWithHttpInfo(
                            apiClient.getTokens(), "application/json", requestBody);
            if (responseInfo != null && responseInfo.getStatusCode() == 200) {
                //排除返回结果为空的情况
                if (responseInfo.getData() != null && responseInfo.getData().getData() != null) {
                    GenerateAffiliateSharingLinkResponseData respData = responseInfo.getData().getData();
                    List<GenerateAffiliateSharingLinkResponseDataAffiliateSharingLinks> affiliateSharingLinks
                            = respData.getAffiliateSharingLinks();
                    //排除链接没有生成的情况
                    if (affiliateSharingLinks != null && !affiliateSharingLinks.isEmpty()) {
                        //目前考虑只给一个商品生成分享链接的情况
                        GenerateAffiliateSharingLinkResponseDataAffiliateSharingLinks shareLink =
                                affiliateSharingLinks.get(0);
                        productShareLinkResponse = new ProductShareLinkResponse();
                        productShareLinkResponse.setProductName(productInfo.getStoreName());
                        productShareLinkResponse.setProductId(material.getId());
                        productShareLinkResponse.setProductImageUrl(material.getCampaignUrl());
                        productShareLinkResponse.setShareLink(shareLink.getAffiliateSharingLink());
                        productShareLinkResponse.setTags(shareLink.getTag());

                        // 异步落地转链记录
                        String platformCashBackRateValue = systemConfigService.getValueByKey(Constants.PLATFORM_CASH_BACK_RATE);
                        BigDecimal platformCashBackRate = new BigDecimal(1.0);
                        if (platformCashBackRateValue != null) {
                            platformCashBackRate = new BigDecimal(platformCashBackRateValue);
                        }
                        StoreProductShareRecord record = new StoreProductShareRecord();
                        record.setUserId(user.getUid() != null ? user.getUid().longValue() : null);
                        record.setTiktokUid(null); // 若无此字段可置null或补充
                        record.setUserAccount(user.getNickname());
                        record.setOriginUrl(Constants.TIKTOK_PRODUCT_URL_PREFIX + productInfo.getOutProductId());
                        record.setShareUrl(shareLink.getAffiliateSharingLink());
                        record.setChannel(channel);
                        record.setProductId(productInfo.getOutProductId());
                        record.setProductName(productInfo.getStoreName());
                        record.setProductPrice(productInfo.getPrice());
                        record.setProductCashbackRate(productInfo.getCashBackRate());
                        record.setUserCashbackRate(productInfo.getCashBackRate().multiply(platformCashBackRate)); // 若有用户返现率字段请补充
                        record.setOperateTime(new java.util.Date());
                        try {
                            // createTime/updateTime 由DB自动生成
                            storeProductShareRecordService.saveAsync(record);
                        } catch (Exception e) {
                            log.error("转链记录存储异常，record：" + JSONUtil.toJsonStr(record));
                        }
                    }
                }
            } else {
                log.error("调用API生成转链失败，productId：{}", id);
            }
        } catch (Exception e) {
            log.error("转链处理异常，productId：{}", id);
            throw new CrmebException("转链处理异常，productId：" + id);
        }
        return productShareLinkResponse;
    }

    /**
     * 根据链接地址获取商品详情
     *
     * @param productUrlSearchRequest 商品url地址
     * @return
     */
    @Override
    public ProductDetailResponse getProductDetailByUrl(ProductUrlSearchRequest productUrlSearchRequest) {
        //1. 根据链接识别电商平台的商品
        ProductChannelEnum channelEnum = tikTokProductIdService.identifyPlatform(productUrlSearchRequest.getUrl());

        //2. 提取商品productId
        String productId = tikTokProductIdService.extractProductId(productUrlSearchRequest.getUrl());
        if (productId == null) {
            //没有识别到
            throw new CrmebException("商品详情地址异常");
        }
        if (productId.length() > 30) {
            ProductDetailResponse productDetailResponse = new ProductDetailResponse();
            productDetailResponse.setDetailUrl(productId);
            return productDetailResponse;
        }
        //3. 本地查询商品信息
        StoreProduct storeProduct = null;
        try {
            storeProduct = storeProductService.getByOutProductId(productId);
        } catch (Exception e) {
            //记录日志，本地没有查询到
        }
        //4. 如果没有查询到，则远程调用查询到商品详情
        if (storeProduct == null) {
            List<StoreProduct> storeProductList = productImportService.importProductsFromTikTok(productId);
            if (CollUtil.isNotEmpty(storeProductList)) {
                storeProduct = storeProductService.getByOutProductId(productId);
            }
        }
        //5. 组装商品详情结果
        ProductDetailResponse productDetailResponse = new ProductDetailResponse();
        productDetailResponse.setProductInfo(storeProduct);

        return productDetailResponse;
    }

    /**
     * 获取商品SKU详情
     *
     * @param id 商品编号
     * @return 商品详情信息
     */
    @Override
    public ProductDetailResponse getSkuDetail(Integer id) {
        // 获取用户
        User user = userService.getInfo();
        SystemUserLevel userLevel = null;
        if (ObjectUtil.isNotNull(user) && user.getLevel() > 0) {
            userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        }

        ProductDetailResponse productDetailResponse = new ProductDetailResponse();
        // 查询商品
        StoreProduct storeProduct = storeProductService.getH5Detail(id);

        // 获取商品规格
        List<StoreProductAttr> attrList = attrService.getListByProductIdAndType(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
        // 根据制式设置attr属性
        productDetailResponse.setProductAttr(attrList);

        // 根据制式设置sku属性
        HashMap<String, Object> skuMap = CollUtil.newHashMap();
        List<StoreProductAttrValue> storeProductAttrValues = storeProductAttrValueService.getListByProductIdAndType(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
        for (StoreProductAttrValue storeProductAttrValue : storeProductAttrValues) {
            StoreProductAttrValueResponse atr = new StoreProductAttrValueResponse();
            BeanUtils.copyProperties(storeProductAttrValue, atr);
            // 设置会员价
            if (ObjectUtil.isNotNull(userLevel)) {
                BigDecimal vipPrice = atr.getPrice().multiply(new BigDecimal(userLevel.getDiscount())).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                atr.setVipPrice(vipPrice);
            }
            skuMap.put(atr.getSuk(), atr);
        }
        productDetailResponse.setProductValue(skuMap);

        return productDetailResponse;
    }

    /**
     * 商品评论列表
     *
     * @param proId            商品编号
     * @param type             评价等级|0=全部,1=好评,2=中评,3=差评
     * @param pageParamRequest 分页参数
     * @return PageInfo<ProductReplyResponse>
     */
    @Override
    public PageInfo<ProductReplyResponse> getReplyList(Integer proId, Integer type, PageParamRequest pageParamRequest) {
        return storeProductReplyService.getH5List(proId, type, pageParamRequest);
    }

    /**
     * 产品评价数量和好评度
     *
     * @return StoreProductReplayCountResponse
     */
    @Override
    public StoreProductReplayCountResponse getReplyCount(Integer id) {
        MyRecord myRecord = storeProductReplyService.getH5Count(id);
        Long sumCount = myRecord.getLong("sumCount");
        Long goodCount = myRecord.getLong("goodCount");
        Long inCount = myRecord.getLong("mediumCount");
        Long poorCount = myRecord.getLong("poorCount");
        String replyChance = myRecord.getStr("replyChance");
        Integer replyStar = myRecord.getInt("replyStar");
        return new StoreProductReplayCountResponse(sumCount, goodCount, inCount, poorCount, replyChance, replyStar);
    }

    /**
     * 获取商品佣金区间
     *
     * @param isSub         是否单独计算分佣
     * @param attrValueList 商品属性列表
     * @param isPromoter    是否推荐人
     * @return String 金额区间
     */
    private String getPacketPriceRange(Boolean isSub, List<StoreProductAttrValue> attrValueList, Boolean isPromoter) {
        String priceName = "0";
        if (!isPromoter) return priceName;
        // 获取一级返佣比例
        String brokerageRatioString = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_STORE_BROKERAGE_RATIO);
        BigDecimal BrokerRatio = new BigDecimal(brokerageRatioString).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
        BigDecimal maxPrice;
        BigDecimal minPrice;
        // 获取佣金比例区间
        if (isSub) { // 是否单独分拥
            maxPrice = attrValueList.stream().map(StoreProductAttrValue::getBrokerage).reduce(BigDecimal.ZERO, BigDecimal::max);
            minPrice = attrValueList.stream().map(StoreProductAttrValue::getBrokerage).reduce(BigDecimal.ZERO, BigDecimal::min);
        } else {
            BigDecimal _maxPrice = attrValueList.stream().map(StoreProductAttrValue::getPrice).reduce(BigDecimal.ZERO, BigDecimal::max);
            BigDecimal _minPrice = attrValueList.stream().map(StoreProductAttrValue::getPrice).reduce(BigDecimal.ZERO, BigDecimal::min);
            maxPrice = BrokerRatio.multiply(_maxPrice).setScale(2, RoundingMode.HALF_UP);
            minPrice = BrokerRatio.multiply(_minPrice).setScale(2, RoundingMode.HALF_UP);
        }
        if (minPrice.compareTo(BigDecimal.ZERO) == 0 && maxPrice.compareTo(BigDecimal.ZERO) == 0) {
            priceName = "0";
        } else if (minPrice.compareTo(BigDecimal.ZERO) == 0 && maxPrice.compareTo(BigDecimal.ZERO) > 0) {
            priceName = maxPrice.toString();
        } else if (minPrice.compareTo(BigDecimal.ZERO) > 0 && maxPrice.compareTo(BigDecimal.ZERO) > 0) {
            priceName = minPrice.toString();
        } else if (minPrice.compareTo(maxPrice) == 0) {
            priceName = minPrice.toString();
        } else {
            priceName = minPrice.toString() + "~" + maxPrice.toString();
        }
        return priceName;
    }

    /**
     * 获取热门推荐商品列表
     *
     * @param pageRequest 分页参数
     * @return CommonPage<IndexProductResponse>
     */
    @Override
    public CommonPage<IndexProductResponse> getHotProductList(PageParamRequest pageRequest) {
        List<StoreProduct> storeProductList = storeProductService.getIndexProduct(Constants.INDEX_HOT_BANNER, pageRequest);
        if (CollUtil.isEmpty(storeProductList)) {
            return CommonPage.restPage(new ArrayList<>());
        }
        CommonPage<StoreProduct> storeProductCommonPage = CommonPage.restPage(storeProductList);

        List<IndexProductResponse> productResponseArrayList = new ArrayList<>();
        for (StoreProduct storeProduct : storeProductList) {
            IndexProductResponse productResponse = new IndexProductResponse();

            productResponse.setId(storeProduct.getId());
            //补充临时的参数
            productResponse.setCashBackRate(storeProduct.getCashBackRate());
            productResponse.setCashBackAmount(storeProduct.getCashBackAmount());
            productResponse.setCashBackRate(new BigDecimal("15"));
            productResponse.setCashBackAmount(new BigDecimal("1.23"));
            productResponse.setChannel(ProductChannelEnum.TIKTOK.getCode());
            productResponse.setMainImageUrl(storeProduct.getImage());
            productResponse.setSalesPrice(storeProduct.getPrice());
            productResponse.setMaxSalesPrice(storeProduct.getMaxSalesPrice());
            productResponse.setMinSalesPrice(storeProduct.getMinSalesPrice());
            productResponse.setTitle(storeProduct.getStoreName());
            productResponse.setShopName(storeProduct.getShopName());


            productResponseArrayList.add(productResponse);
        }
        CommonPage<IndexProductResponse> productResponseCommonPage = CommonPage.restPage(productResponseArrayList);
        BeanUtils.copyProperties(storeProductCommonPage, productResponseCommonPage, "list");
        return productResponseCommonPage;
    }

    /**
     * 商品详情评论
     *
     * @param id 商品id
     * @return ProductDetailReplyResponse
     * 评论只有一条，图文
     * 评价总数
     * 好评率
     */
    @Override
    public ProductDetailReplyResponse getProductReply(Integer id) {
        return storeProductReplyService.getH5ProductReply(id);
    }

    /**
     * 优选商品推荐
     *
     * @return CommonPage<IndexProductResponse>
     */
    @Override
    public CommonPage<IndexProductResponse> getGoodProductList() {
        PageParamRequest pageRequest = new PageParamRequest();
        List<StoreProduct> storeProductList = storeProductService.getIndexProduct(Constants.INDEX_BEST_BANNER, pageRequest);
        if (CollUtil.isEmpty(storeProductList)) {
            return CommonPage.restPage(new ArrayList<>());
        }
        CommonPage<StoreProduct> storeProductCommonPage = CommonPage.restPage(storeProductList);
        List<IndexProductResponse> productResponseArrayList = new ArrayList<>();
        for (StoreProduct storeProduct : storeProductList) {
            IndexProductResponse productResponse = getIndexProductResponse(storeProduct);
            productResponseArrayList.add(productResponse);
        }
        CommonPage<IndexProductResponse> productResponseCommonPage = CommonPage.restPage(productResponseArrayList);
        BeanUtils.copyProperties(storeProductCommonPage, productResponseCommonPage, "list");
        return productResponseCommonPage;
    }

    /**
     * 商品列表(个别分类模型使用)
     *
     * @param request          列表请求参数
     * @param pageParamRequest 分页参数
     * @return CommonPage
     */
    @Override
    public CommonPage<IndexProductResponse> getCategoryProductList(ProductListRequest request, PageParamRequest pageParamRequest) {
        ProductRequest searchRequest = new ProductRequest();
        BeanUtils.copyProperties(searchRequest, request);
        List<StoreProduct> storeProductList = storeProductService.findH5List(searchRequest, pageParamRequest);
        if (CollUtil.isEmpty(storeProductList)) {
            return CommonPage.restPage(new ArrayList<>());
        }
        CommonPage<StoreProduct> storeProductCommonPage = CommonPage.restPage(storeProductList);

        List<IndexProductResponse> productResponseArrayList = new ArrayList<>();
        for (StoreProduct storeProduct : storeProductList) {
            IndexProductResponse productResponse = getIndexProductResponse(storeProduct);
            productResponseArrayList.add(productResponse);
        }
        CommonPage<IndexProductResponse> productResponseCommonPage = CommonPage.restPage(productResponseArrayList);
        BeanUtils.copyProperties(storeProductCommonPage, productResponseCommonPage, "list");
        return productResponseCommonPage;
    }

    /**
     * 获取商品排行榜
     *
     * @return List
     */
    @Override
    public List<StoreProduct> getLeaderboard() {
        return storeProductService.getLeaderboard();
    }

}

